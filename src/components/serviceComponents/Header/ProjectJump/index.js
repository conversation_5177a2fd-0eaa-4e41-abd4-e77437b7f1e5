import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import { DKLogoIcon } from '@/assets/icon';
import styles from '@/components/serviceComponents/Header/style.module.less';

const cx = classBind.bind(styles);

const ProjectJump = () => {
  const gotoDkHomeFromIcon = () => {
    window.__OmegaEvent('ep_dkpc_icon_returndk_ck');
  };


  return (
    <>
      <div id='iPortal-entry-dk' />
      <a
        href="/knowledge"
        onClick={gotoDkHomeFromIcon}>
        <img
          src={DKLogoIcon}
          className={cx('dk-logo')}
          alt={intl.t('知识库')}
        />
      </a>
    </>
  );
};

export default ProjectJump;
