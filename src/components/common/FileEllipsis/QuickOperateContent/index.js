import { Tooltip } from 'antd';
import { intl } from 'di18n-react';
import { useState, useEffect } from 'react';
import starFile from '@/components/CooperOperation/StarFile';
import {
  MY_COOPER,
  TEAM_COOPER,
  SHARE_FROM_ME,
  SHARE_WITH_ME,
  STAR,
} from '@/constants/cooper';
import classBind from 'classnames/bind';
import styles from './style.module.less';

const cx = classBind.bind(styles);

export default function QuickOperateContent(resourceId, resourceType) {
  const [isStared, setIsStared] = useState(false);
  const [isQuicked, setIsQuicked] = useState(false);

  const handleStar = () => {

  }

  const handleQuick = () => {

  }

  const handleShare = () => {

  }

  return (
    <div className={'quick-operate-wrap'}>
      <Tooltip title={isStared ? intl.t('取消收藏') : intl.t('收藏')} >
        <div
          className={cx('filename-side-star', 'quick-operate-icon')}
          onClick={(e) => handleStar(e)}
          data-e2e='collect'
      >
          {isStared ? <i
            className={
              cx('dk-iconfont', 'dk-icon-yishoucang1', 'icon-selected', 'icon-selected-star')
            }
        /> : <i
          className={
            cx('dk-iconfont', 'dk-icon-shoucang4px')
          }
        />}
        </div>
      </Tooltip>
      <Tooltip title={isStared ? intl.t('添加至快速访问') : intl.t('从“快速访问”移出')} >
        <div
          className={cx('filename-side-quicked', 'quick-operate-icon')}
          onClick={(e) => handleQuick(e)}
          data-e2e='collect'
      >
          {isQuicked ? <i
            className={
            cx('dk-iconfont', 'dk-icon-kuaisufangwenmian', 'icon-selected')
          }
        /> : <i
          className={
            cx('dk-iconfont', 'dk-icon-kuaisufangwen3px')
          }
        />}
        </div>
      </Tooltip>
      <Tooltip title={intl.t('分享')} >
        <div
          className={cx('filename-side-share', 'quick-operate-icon')}
          onClick={(e) => handleShare(e)}
          data-e2e='collect'
      >
          <i
            className={
            cx('dk-iconfont', 'dk-icon-fenxiang4px')
          }
          />
        </div>
      </Tooltip>
    </div>
  );
}

