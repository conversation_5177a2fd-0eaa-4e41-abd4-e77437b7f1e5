.quick-operate-wrap {
  display: flex;
  align-items: center;

  .quick-operate-icon {
    margin-left: 2px;
    height: 24px;
    line-height: 24px;
    width: 24px;
    text-align: center;
    // background-color: @blueGray-10;
    border-radius: 4px;

    // :global(.dk-iconfont) {
    //   line-height: inherit;
    // }

    &:hover {
      background-color: @blueGray-10;
    }
  }

  .icon-selected {
    color: @primary-color;
    width: 22px;
    height: 22px;
    border-radius: 4px;
  }

  .icon-selected-star {
    color: #F6C746;
  }

}