import { getUserNameFromCookie } from '@/utils';
import { getLocale } from 'di18n-react';
import { useEffect } from 'react';


function IPortal(props) {
  useEffect(() => {
    const popupCooperSDK = new window.PopupSDK({
      // mountElement: document.getElementById('mount-point'), // 必填，挂载点元素
      mountSelector: '#iPortal-entry-cooper', // 必填，挂载点元素
      // customClass: 'my-popup',
      appId: '11', // 必填
      appSecretKey: '25C3CF82715DE8FCD0D411CC6F1E32F7', // 必填
      secretKey: '0E557393BE781A93206891F02687468F', // 必填
      ldap: getUserNameFromCookie(), // 必填
      onOpen: () => console.log('弹窗已打开'),
      onClose: () => console.log('弹窗已关闭'),
      onSelect: (item) => console.log('选中应用onSelect:', item),
    });
    window.popupCooperSDK = popupCooperSDK;
  }, [])

  useEffect(() => {
    window.popupCooperSDK && window.popupCooperSDK.updateConfig({
      lang: getLocale(),
    });
  }, [getLocale()]);

  return (
    <div id='iPortal-entry-cooper' />
  );
}

export default IPortal;
