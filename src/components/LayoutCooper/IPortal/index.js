
import { getUserNameFromCookie } from '@/utils';
import { getLocale } from 'di18n-react';
import { useEffect } from 'react';


function IPortal(props) {
  const { mountSelector, appId, appSecretKey, secretKey, id, children } = props;
  useEffect(() => {
    if (window.popupCooperSDK) return;
    const popupCooperSDK = new window.PopupSDK({
      // mountElement: document.getElementById('mount-point'), // 必填，挂载点元素
      mountSelector: '#iPortal-entry-cooper', // 必填，挂载点元素
      // customClass: 'my-popup',
      appId: '11', // 必填
      appSecretKey: '25C3CF82715DE8FCD0D411CC6F1E32F7', // 必填
      secretKey: '0E557393BE781A93206891F02687468F', // 必填
      ldap: getUserNameFromCookie(), // 必填
      onOpen: () => console.log('弹窗已打开'),
      onClose: () => console.log('弹窗已关闭'),
      onSelect: (item) => console.log('选中应用onSelect:', item),
    });
    window.popupCooperSDK = popupCooperSDK;
  }, [])

  const locale = getLocale();
  useEffect(() => {
    /**
     * 支持动态更新ldap、lang配置：
     * 1. 在前端框架中，可在watch监听器或useEffect钩子中调用updateConfig方法
     * 2. 对于异步获取的ldap、lang数据，可在用户点击图标前更新配置
     */
    window.popupCooperSDK && window.popupCooperSDK.updateConfig({
      lang: locale,
    });
  }, [locale]);

  return (
    children || <div id={id} />
  );
}

export default IPortal;
