.no-width {
  width: 0;
  opacity: 0;
}

.large-wrap {
  .logo-header-wrapper {
    display: flex;
    align-items: center;
    // padding: 0 16px;
    margin: 10px 16px 0px;
  }

  .aside-large {
    width: 240px;

    .item-name-normal {
      padding: 7px 24px;
    }

    .item-name {
      height: 34px;
    }

    .split-line {
      width: 100%;
      height: 1px;
      background-color: @blueGray-16;
      margin-top: 4px;
      margin-bottom: 6px;
    }
  }

  .pine-space {
    width: 100%;

    padding: 0 8px;

    :global {
      .space-item-nav {
        padding-left: 46px !important;
      }
    }
  }
}