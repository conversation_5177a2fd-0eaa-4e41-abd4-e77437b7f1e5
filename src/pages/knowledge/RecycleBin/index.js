import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import { Helmet } from 'react-helmet';
import { RecycleBinIcon } from '@/assets/icon';
import commonStyles from '@/components/serviceComponents/Layout/style.module.less';
import { useSelector } from 'react-redux';
import { useEffect } from 'react';
import RecycleBinList from './RecycleBinList';
import styles from './style.module.less';

const cx = classBind.bind(styles);
const cm = classBind.bind(commonStyles);

function RecycleBin() {
  const { knowledgeDetail } = useSelector((state) => state.KnowledgeData);
  useEffect(() => {
    if (knowledgeDetail.spaceName) {
      window.document.title = intl.t('回收站-{slot0}', {
        slot0: knowledgeDetail.spaceName,
      });
    }
  }, [knowledgeDetail]);

  return (
    <div className={`${cx('recycleBinWrap')} ${cm('body-main-wrap')}`}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/dk-logo.png" />
      </Helmet>
      <div className={cm('mainTitle')}>
        {/* <img
           src={RecycleBinIcon}
           className={cm('titleIcon')} /> */}
        <div className={cm('title')}>{intl.t('回收站')}</div>
      </div>
      <RecycleBinList />
    </div>
  );
}

export default RecycleBin;
