import { Spin } from 'antd';
import classNames from 'classnames/bind';
import { useEffect, useMemo, useContext, useState } from 'react';
import { Helmet } from 'react-helmet';
import { connect, useSelector } from 'react-redux';
import { inPhone, isPreviewFile } from '@/utils';
import Editor from './Editor';
import EditorNew from './EditorNew';
import Authority from './Authority';
import SharePreview from '@/components/serviceComponents/SharePreview';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { getIsWikiHTML } from '@/constants/wikihtmlrenderHelper';
import WikiHTMLRender from '@/pages/knowledge/ShareBook/SharePageDetail/Wiki';
import styles from '@/pages/knowledge/PageDetail/style.module.less';

const cls = classNames.bind(styles);

const isInPhone = inPhone();
function SharePageDetail(props) {
  const { docInfo, checkToEditor, newEditorMsg } = props;
  const { latestVersion, docType, pageName, pageStyle = {}} = docInfo;
  const { initLoading } = props;
  const isPreview = isPreviewFile(docType);
  const isWikiHTML = docInfo.externalSystemType ? getIsWikiHTML(docInfo) : false;
  const { shareId, pageId } = useContext(LayoutContext);
  const { shareModalSetting } = useSelector((state) => state.SharePage);
  const [isReady, setIsReady] = useState(false);

  // 调用check接口判断是否为新编辑器
  useEffect(() => {
    if (!docInfo.guid) {
      return;
    }

    setIsReady(false);
    checkToEditor({ guid: docInfo.guid }).finally(() => {
      setIsReady(true);
    });
  }, [docInfo.guid])

  // 0 不可下载；1可下载
  const hasCommentPerm = useMemo(() => {
    return shareModalSetting?.sharePermList?.comment === 1;
  }, [shareModalSetting]);

  const hasDownloadPerm = useMemo(() => {
    return shareModalSetting?.sharePermList?.download === 1;
  }, [shareModalSetting])

  useEffect(() => {
    document.querySelector('div[class^=main-layout-dk]').style.minWidth = '400px';
  }, []);

  const needHeader = useMemo(() => {
    return !isInPhone;
  }, []);

  const getDocView = () => {
    if (isPreview) {
      return (
        <SharePreview
          fileId={latestVersion}
          fileName={pageName}
          pageId={docInfo.pageId}
          shareId={shareId}
          isDkShare={!!pageId}
        />
      )
    }
    if (isWikiHTML) {
      return <WikiHTMLRender />
    }

    if (isReady && newEditorMsg?.useEditorNew === true) {
      return (
        <EditorNew
          shareId={shareId}
          pageId={docInfo?.pageId}
          hasDownloadPerm={hasDownloadPerm}
          hasCommentPerm={hasCommentPerm}
        />
      );
    }

    if (isReady) {
      return (
        <Editor
          hasDownloadPerm={hasDownloadPerm}
          hasCommentPerm={hasCommentPerm}
        />
      );
    }

    return <></>
  }

  return (
    <div
      className={
      cls(
        'view-share-document-detail',
        'page-style-root',
        `font-size-${pageStyle?.preFontSize}`,
        `screen-${pageStyle?.preScreen}`,
      )}
    >
      <Helmet>
        <link
          rel='shortcut icon'
          type='image/png'
          href='//img-ys011.didistatic.com/static/cooper_cn/dk-logo.png'
        />
      </Helmet>
      <Authority>
        <div
          className={cls('page-content', {
            'no-header': !needHeader,
            'dk-editor-in-phone_reset': isInPhone,
          })}
        >
          {
            !isPreview && initLoading && (
              <div className={'page-detail-loading'}>
                <Spin/>
              </div>
            )
          }
          { getDocView() }
        </div>
      </Authority>
    </div>
  );
}

function mapStateToProps({ SharePage }) {
  const { initLoading, docInfo, newEditorMsg } = SharePage;
  return {
    initLoading,
    docInfo,
    newEditorMsg,
  };
}

function mapDispatchToProps({ SharePage }) {
  const { getSharePageDetail, changeInitLoading, checkToEditor } = SharePage;
  return {
    getSharePageDetail,
    changeInitLoading,
    checkToEditor,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(SharePageDetail);
