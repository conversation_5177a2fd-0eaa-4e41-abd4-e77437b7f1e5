import { getUrlParams } from '@/utils';
import { Helmet } from 'react-helmet';
import CreateKnowledge from './CreateKnowledge';

function Create() {
  const mapperSpaceId = getUrlParams('mapperSpaceId');

  return (
    <div className={'create-wrap'}>
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/dk-logo.png" />
      </Helmet>
      <CreateKnowledge mapperSpaceId={mapperSpaceId} />
    </div>
  );
}

export default Create;
