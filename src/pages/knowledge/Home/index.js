import { intl } from 'di18n-react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import classBind from 'classnames/bind';
import Avatar from 'antd/lib/avatar/avatar';
import ShareModal from '@/components/serviceComponents/ShareModal';
import { SETUP_TABS } from '@/constants/setup';
import { DKDefaultIcon, departmentTagIcon } from '@/assets/icon/index';
import { useEffect, useState } from 'react';
import usePermission from '@/hooks/usePermission';
import { SHARE_TYPE } from '@/constants/index';
import Trends from './Trends';
import styles from './style.module.less';
import { inPhone } from '@/utils';

const cx = classBind.bind(styles);

function Home() {
  const dispatch = useDispatch();
  const { getKnowledgeMember } = dispatch.KnowledgeData;

  // eslint-disable-next-line no-unused-vars
  const { knowledgeDetail, isCreatedPage, permission } = useSelector(
    (state) => state.KnowledgeData,
  );
  const navigate = useNavigate();
  const { knowledgeId } = useParams();
  const { teamId } = useParams();

  const { checkOperationPermission } = usePermission();

  const [hasSharePerm, setHasSharePerm] = useState(true);
  const [knowledgeMember, setKnowledgeMember] = useState([]);

  const gotoMemberManager = () => {
    window.__OmegaEvent('ep_dkpc_pagehome_membermanage_ck');
    navigate(
      teamId
        ? `/team-file/${teamId}/knowledge/${knowledgeId}/setUp?key=${SETUP_TABS.POWER_MANAGERMENT}`
        : `/knowledge/${knowledgeId}/setUp?key=${SETUP_TABS.POWER_MANAGERMENT}`,
    );
  };

  useEffect(() => {
    getKnowledgeMember(knowledgeId).then((res) => {
      setKnowledgeMember(res);
    });
  }, [knowledgeId]);

  useEffect(() => {
    if (knowledgeDetail.spaceName) {
      window.document.title = intl.t('首页-{slot0}', {
        slot0: knowledgeDetail.spaceName,
      });
    }

    const hasPerm = checkOperationPermission(
      'CONTROL_DK_SHARE',
      permission?.perm,
    );

    setHasSharePerm(hasPerm);
  }, [knowledgeDetail]);

  useEffect(() => {
    const showAi = window.editorIframeABSwitch;

    if (showAi && !(inPhone())) {
      try {
        window.CooperClientService?.create({})
      } catch (error) {}
    }

    return () => {
      try {
        window.CooperClientService?.destroy();
      } catch (error) {}
    }
  }, [])

  return (
    <div className={cx('home-wrap')}>
      <Helmet>
        <title>知识库-Cooper</title>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/dk-logo.png"
        />
      </Helmet>
      <div className={cx('mainTitle')}>
        <img
          className={cx('exPicture')}
          src={knowledgeDetail.exPicture || DKDefaultIcon}
        />

        <div className={cx('text-content')}>
          <div className={cx('title-wrap')}>
            <h1 className={cx('title')}>
              <span className={cx('dkName')}>{knowledgeDetail.spaceName}</span>
              {knowledgeDetail.department && (
                <span className={cx('department')}>
                  {intl.t('部门')}
                </span>
                // <img src={departmentTagIcon} className={cx('departmentTag')} />
              )}
            </h1>
            <ShareModal
              resourceId={knowledgeId}
              resourceName={knowledgeDetail.spaceName}
              shareType={SHARE_TYPE.DK}
              hasPermission={hasSharePerm}
            />
          </div>
          <p className={cx('desc')}>{knowledgeDetail.exDesc}</p>
          <div className={cx('memberAvatar')}>
            {knowledgeMember.slice(0, 5).map((item) => (
              <Avatar
                key={item.id}
                className={cx('avatar')}
                alt=""
                src={item.avatar}
              />
            ))}

            {knowledgeMember.length > 5 && (
              <span
                onClick={gotoMemberManager}
                className={cx('avatar', 'more')}
              >
                ...
              </span>
            )}
            {knowledgeMember.length > 0 && (
              <div onClick={gotoMemberManager}>
                <i
                  className={cx(
                    'dk-iconfont',
                    'dk-icon-youjiantou1',
                    'homeMoreAvatar',
                  )}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      <Trends />
    </div>
  );
}

export default Home;
