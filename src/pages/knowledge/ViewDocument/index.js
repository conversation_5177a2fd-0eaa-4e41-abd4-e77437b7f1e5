import { Spin } from 'antd';
import classNames from 'classnames/bind';
import { useContext, useMemo, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Helmet } from 'react-helmet';
import Authority from './Authority';
import Editor from '../PageDetail/Editor';
import EditorNew from './EditorNew';
import { isPreviewFile } from '@/utils';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import Preview from '@/components/serviceComponents/Preview';
import useGetPreviewFileInfo from '@/hooks/useGetPreviewFileInfo';
import { getIsWikiHTML } from '@/constants/wikihtmlrenderHelper';
import WikiHTMLRender from '../PageDetail/Wiki';
import styles from '@/pages/knowledge/PageDetail/style.module.less';

const cls = classNames.bind(styles);

function ViewDocument({ initLoading, docType, docInfo, fileId, permission, multiPath = [], pageStyle = {}, newEditorMsg, checkToEditor }) {
  const { pageId } = useContext(LayoutContext);
  const isPreview = isPreviewFile(docType);
  const isWikiHTML = docInfo.externalSystemType ? getIsWikiHTML(docInfo) : false;
  const { loading } = useGetPreviewFileInfo(isPreview, fileId, 0);
  const [isReady, setIsReady] = useState(false);


  const parentId = useMemo(() => {
    if (!multiPath) return;
    return multiPath[multiPath?.length - 2 ?? 0]?.id;
  }, [multiPath, pageId]);

  // 在灰度时，调用check接口转换为新文档数据
  useEffect(() => {
    if (!docInfo.guid) {
      return;
    }

    // wiki html渲染渲染不走新文档流程
    if (getIsWikiHTML(docInfo)) {
      return;
    }
    setIsReady(false);
    checkToEditor({
      guid: docInfo.guid,
      latestVersion: docInfo.latestVersion,
    }).finally(() => {
      setIsReady(true);
    });
  }, [docInfo])

  const getDocView = () => {
    if (isPreview) {
      return <Preview
        permission={permission}
        parentId={parentId}
        isEdit={false}
      />
    }
    if (isWikiHTML) return <WikiHTMLRender />;

    if (isReady && newEditorMsg.useEditorNew === true) {
      return <EditorNew />;
    }

    if (isReady) {
      return (
        <Editor
          editorProps={{
            comment: {
              show: false,
              editable: false,
            },
            dictionary: {
              show: false,
              editable: false,
            },
            draggable: false,
            title: false,
            menu: {},
            collab: {
              collabUser: false,
              collabUnderLine: false,
              collabCursor: false,
            },
          }}
        />
      );
    }

    return <></>
  }

  useEffect(() => {
    document.querySelector('div[class^=main-layout-dk]').style.minWidth = '400px';
  }, []);

  return (
    <div className={cls('view-document-detail', 'page-detail', 'page-style-root', `font-size-${pageStyle?.preFontSize}`, `screen-${pageStyle?.preScreen}`)}>
      <Helmet>
        <link
          rel='shortcut icon'
          type='image/png'
          href='//img-ys011.didistatic.com/static/cooper_cn/dk-logo.png'
        />
      </Helmet>
      <Authority
        isPreview={isPreview}
        showHeader={false}
        showComment={false}
      >
        <div
          className={cls('page-content', {
            'no-header': true,
            'dk-editor-in-phone_reset': false,
          })}
        >
          <div>
            {(isPreview ? loading : initLoading) && (
              <div className={'page-detail-loading'}>
                <Spin />
              </div>
            )}
          </div>
          { getDocView() }
        </div>
      </Authority>
    </div>
  );
}

function mapStateToProps({ pageDetail }) {
  const { initLoading, docInfo, previewFileId, newEditorMsg } = pageDetail;
  const { docType, permission, multiPath, pageStyle } = docInfo;
  return {
    initLoading,
    fileId: previewFileId.inDetail,
    docType,
    docInfo,
    permission,
    multiPath,
    pageStyle,
    newEditorMsg,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const { changeInitLoading, checkToEditor } = pageDetail;
  return {
    changeInitLoading,
    checkToEditor,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(ViewDocument);
