import { Spin } from 'antd';
import classNames from 'classnames/bind';
import { useContext, useEffect, useMemo, useState } from 'react';
import { connect } from 'react-redux';
import { Helmet } from 'react-helmet';
import Authority from './Authority';
import Editor from './Editor';
import EditorNew from './EditorNew';
import { addRecentVisit } from '@/service/knowledge/recent';
import { inPhone, isDkSheet, isPreviewFile, isAndroid, isIpad } from '@/utils';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import Preview from '@/components/serviceComponents/Preview';
import ShimoExcelPreview from '@/components/ShimoExcelPreview';
import { RECENT_VISIT_TYPE } from '@/constants/recent';
import useGetPreviewFileInfo from '@/hooks/useGetPreviewFileInfo';
import WikiHTMLRender from './Wiki';
import { getIsWikiHTML } from '@/constants/wikihtmlrenderHelper';
import ErrorBoundary from '@/components/ErrorBoundary';
import { getDcVersion, isBeforeVersion } from '@/utils/dc-helper';
// import EditorTitleContent from '@/pages/knowledge/EditorTitleContent';
import styles from '@/pages/knowledge/PageDetail/style.module.less';

const cls = classNames.bind(styles);

const isInPhone = inPhone();
const isInAndroid = isAndroid();
function PageDetail({ checkToEditor, newEditorMsg, initLoading, docType, docInfo, fileId, permission, changeEditReady, multiPath = [], pageStyle = {}}) {
  const { knowledgeId, pageId } = useContext(LayoutContext);
  const isPreview = isPreviewFile(docType);
  const isWikiHTML = getIsWikiHTML(docInfo);
  const isSheet = isDkSheet(docType);
  const { loading } = useGetPreviewFileInfo(isPreview, fileId, 0);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      addRecentVisit({
        resourceId: pageId,
        sourceType: RECENT_VISIT_TYPE.NORMAL,
      });
    }, 4000);
    window.performance.mark(`catalog-nodeClick-start${pageId}`);
  }, [pageId]);

  useEffect(() => {
    return () => {
      setIsReady(false);
    };
  }, []);

  // 在灰度时，调用check接口转换为新文档数据
  useEffect(() => {
    if (!docInfo.guid) {
      return;
    }

    // wiki html渲染渲染不走新文档流程
    if (getIsWikiHTML(docInfo)) {
      return;
    }

    checkToEditor({
      guid: docInfo.guid,
      latestVersion: docInfo.latestVersion,
    }).finally(() => {
      setIsReady(true);
    });
  }, [docInfo])

  const parentId = useMemo(() => {
    if (!multiPath) {
      return;
    }
    return multiPath[multiPath?.length - 2 ?? 0]?.id;
  }, [multiPath, pageId]);

  const getDocView = () => {
    if (isPreview) {
      window.isInitViewMark = false;
      changeEditReady(true);
      return (
        <Preview
          permission={permission}
          parentId={parentId}
          isEdit={false}
        />
      )
    }
    if (isWikiHTML) {
      window.isInitViewMark = false;
      changeEditReady(true);
      return <WikiHTMLRender />;
    }
    if (isSheet) {
      window.isInitViewMark = false;
      changeEditReady(true);
      return <ShimoExcelPreview/>;
    }

    if (isReady && (newEditorMsg.useEditorNew === false || newEditorMsg.useEditorNew === null)) {
      return <Editor />;
    }

    if (isReady && newEditorMsg.useEditorNew === true) {
      changeEditReady(true);
      return <EditorNew />;
    }

    return <></>
  }

  // 手机端，隐藏底部导航栏
  useEffect(() => {
    if (inPhone() && !isIpad()) {
      const dcVersion = getDcVersion();
      if (!isBeforeVersion(dcVersion, '4.12.0')) {
        window.dcH5Sdk.app.appSetOrientation({
          orientation: 'all',
          onSuccess: () => console.log('appSetOrientation is success!!'),
          onFail: () => {},
        });
        if (!isInAndroid) {
          window.dcH5Sdk.event.on('orientationChange', (data) => {
            if (data.orientation === 'portrait') {
              window.dcH5Sdk.navigation.hideToolBar({
                visibility: true,
                onSuccess: () => {},
                onFail: () => {},
              });
            } else {
              window.dcH5Sdk.navigation.hideToolBar({
                visibility: false,
                onSuccess: () => {},
                onFail: () => {},
              });
            }
          })
        }
      }
    }
  }, [])

  return (
    <div
      className={
        cls(
          'page-detail',
          'page-detail-view',
          'page-style-root',
          `font-size-${pageStyle?.preFontSize}`,
          `screen-${pageStyle?.preScreen}`,
          {
            'page-detail-inphone': isInPhone,
          },
        )
      }
    >
      <Helmet>
        <link
          rel="shortcut icon"
          type="image/png"
          href="//img-ys011.didistatic.com/static/cooper_cn/dk-logo.png"
        />
      </Helmet>
      <Authority isPreview={isPreview}>
        {
          (isPreview ? loading : initLoading) && (
            <div className={'page-detail-loading'}>
              <Spin />
            </div>
          )
        }
        <div
          className={cls('page-content', {
            'no-header': isInPhone,
            'dk-editor-in-phone_reset': isInPhone,
          })}
        >
          <ErrorBoundary>
            { getDocView() }
          </ErrorBoundary>
        </div>
      </Authority>
    </div>
  );
}

function mapStateToProps({ pageDetail }) {
  const { initLoading, docInfo, previewFileId, newEditorMsg } = pageDetail;
  const { docType, permission, multiPath, pageStyle } = docInfo;
  return {
    initLoading,
    fileId: previewFileId.inDetail,
    docType,
    permission,
    multiPath,
    pageStyle,
    docInfo,
    newEditorMsg,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const { changeInitLoading, changeEditReady, checkToEditor } = pageDetail;
  return {
    changeInitLoading,
    changeEditReady,
    checkToEditor,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(PageDetail);
