<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="google" content="notranslate" />
  <meta property="og:title" content="知识管理平台" />
  <meta property="og:description" content="高效便捷的知识管理平台" />
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
  <meta name="theme-color" content="#000" />
  <script>
    if (location.protocol === "https:") {
      document.write(
        '<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">'
      );
    }
  </script>
  <link rel="dns-prefetch" href="https://img-ys011.didistatic.com" />
  <link rel="dns-prefetch" href="https://cooper.didichuxing.com/m/didoceditor/" />
  <link rel="dns-prefetch" href="https://s3-conveyor.didiglobal.com" />
  <link rel="dns-prefetch" href="https://as.xiaojukeji.com" />
  <link rel="dns-prefetch" href="https://sec-aegisfe.didistatic.com" />
  <link rel="dns-prefetch" href="https://img-hxy021.didistatic.com" />
  <link rel="dns-prefetch" href="https://img-ys011.didistatic.com" />
  <link rel="dns-prefetch" href="https://omgup2.xiaojukeji.com" />
  <link rel="dns-prefetch" href="https://water-mark.xiaojukeji.com" />
  <title>知识库</title>
</head>

<body class="didoc2-editor-app__wrapper">
  <div id="root">
  </div>
  <script type="text/javascript">
    window.uploadCount = {
      sum: 0,
      cancel: 0,
      success: 0,
    };
    // mark lcp指标供omega收集
    try {
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (/js-wartermark/.test(entry.id)) return;
          //自定义上传的图片、video、svg中的图片、background【文件、图片、流程图】
          if (/s3|flow_chart/.test(entry.url)) return;
          if (
            entry.element?.className.includes("publish-icon") ||
            entry.element?.className.includes("emptyIcon")
          )
            return;
          if (entry.element === null) return;
          let className =
            entry.element?.className ||
            entry.element?.parentNode?.className ||
            entry.element?.parentNode?.parentNode?.className;
          if (className?.indexOf(" ") !== -1)
            className = className.split(" ")?.[0];
          // 编辑器页面中的标签
          if (document.querySelector(`#knowledge_editor_box ${className}`))
            return;
          console.log("LCP candidate:", entry.startTime, entry);
          performance && performance.mark("largest-contentful-paint");
        }
      }).observe({ type: "largest-contentful-paint", buffered: true });
    } catch (error) { }
  </script>
  <script type="text/javascript">
    try {
      window.startTimeLogin = new Date().getTime();
      window.startTime = window.performance.now();
      window.performance.mark("page-start");
      const [_, _1, knowledgeId, pageId, isEdit] =
        window.location.pathname.split("/");
      const isDoc = knowledgeId && /^\d{13}$/.test(pageId);
      window.isInitViewMark = isDoc && !isEdit;
      window.isInitEditMark = isDoc && isEdit === "edit";
      window.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'hidden') {
          window.isInitViewMark = false;
          window.isInitEditMark = false;
        }
      });
    } catch (e) {
      console.log(e);
    }
  </script>
  <script type="text/javascript">
    function getUserNameFromCookie() {
      const m = document.cookie?.match(/username=([^;]*)/);
      return decodeURIComponent(m ? m[1] : "");
    }
    let env = "<%= htmlWebpackPlugin.options.env_my %>";
   // https://omega.xiaojukeji.com/omegabook/sdk/webLite/
    window.Omega = window.Omega || {
        appKey: env === "prod" ? "omega960d913d18" : "omega2e0624685b",
        autoResourceError: true, // 打开静态资源监控
        // autoClick: false,
        autoPosition: false,
        hashRouterEnable: true,
        browserRouterEnable: false,
        autoSendPageView: false,
        fastLoad: true,
        crashMonitor: { // 页面崩溃
          type: 'onload',
          crashTime: 18 // s
        },
        // jankMonitor: {  // 页面卡顿 - 持续监测，对性能有影响；
        //   type: 'onload', // onevent 在页面触发’click’或’keydown’后的某段时间内进行检测。
        // },
        autoWhiteScreenMonitor: {
          container: '#root', // 白屏监控的目标根节点
          childrenDepth: 6,  // 监听深度 默认为2 监听到根节点下一层
          durationSeconds: 20000, // 白屏持续时间 单位ms
        },
        // jsErrorFilters: [
        //   { message: (lcainfo)|(sse) } //过滤无用的错误信息
        // ],
        // autoPerformance: false, //注意关闭性能上报，避免和npm中引入的性能监控 重复上报
        userName: getUserNameFromCookie(),
        lcpFilter(dom) {
          return /js-wartermark/.test(dom.id);
        },
      };
    fetch(
      `https://as.xiaojukeji.com/ep/as/feature?name=di-knowledge&key=${getUserNameFromCookie()}&_cooper_username=${getUserNameFromCookie()}&__caller=di-knowledge`
    )
      .then((res) => res.json())
      .then((res) => {
        if (res.code === 0 && res && res.data) {
          let result = res.data[0] || {};
          if (result.allow) {
            window.editorIframeABSwitch = true;
          }
        }
      })
      .catch((e) => {
        console.log("获取灰度名单失败", e);
      });
    fetch(
      `https://as.xiaojukeji.com/ep/as/feature?name=didoc-iframe&key=${getUserNameFromCookie()}&_cooper_username=${getUserNameFromCookie()}&__caller=di-knowledge`
    )
      .then((res) => res.json())
      .then((res) => {
        if (res.code === 0 && res && res.data) {
          let result = res.data[0] || {};
          if (result.allow) {
            window.didocIframeABSwitch = true;
          }
        }
      })
      .catch((e) => {
        console.log("获取灰度名单失败", e);
      });
    fetch(
      `https://as.xiaojukeji.com/ep/as/feature?name=block-editor&key=${getUserNameFromCookie()}&_cooper_username=${getUserNameFromCookie()}&__caller=di-knowledge`
    )
      .then((res) => res.json())
      .then((res) => {
        if (res.code === 0 && res && res.data) {
          let result = res.data[0] || {};
          if (result.allow) {
            window.editorBlockABSwitch = true;
          }
        }
      })
      .catch((e) => {
        console.log("获取灰度名单失败", e);
      });
    // fetch(
    //   `https://as.xiaojukeji.com/ep/as/feature?name=cooper_import_wiki_as_html&key=${getUserNameFromCookie()}&_cooper_username=${getUserNameFromCookie()}&__caller=di-knowledge`
    // )
    //   .then((res) => res.json())
    //   .then((res) => {
    //     if (res.code === 0 && res && res.data) {
    //       let result = res.data[0] || {};
    //       if (result.allow) {
    //         window.cooperImportWikiAsHtml = true;
    //       }
    //     }
    //   })
    //   .catch((e) => {
    //     console.log("获取灰度名单失败", e);
    //   });
  </script>
  <script>
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries().filter(item => item.name.includes('img-ys011.didistatic.com/static/cooper_cn/'));
        if (entries.length !== 0) {
          let isFromCache = true;
          entries.length > 0 && entries.forEach(entry => {
            if(entry.initiatorType === 'script') {
              if (entry.fetchStart !== entry.responseEnd) {
                isFromCache = false
                return
              }
            }
          });
          window.isFromCache = isFromCache
        }
      });
      observer.observe({ entryTypes: ['resource'] });
      window.addEventListener('load', () => {
        if (sessionStorage.getItem('shouldScrollTolastPos') === 'true') {
          setTimeout(() => {
            window.didocScrollTo?.('lastPos');
            sessionStorage.setItem('shouldScrollTolastPos', 'false');
          }, 3000);
        }
        observer.disconnect();
      });
    } catch (error) {
      console.log(error);
    }
  </script>
  <script src="https://img-ys011.didistatic.com/static/cooper_cn/livechat/main.576e0610.js"></script>
  <script type="text/javascript" crossorigin="Anonymous"
    src="https://tracker.didistatic.com/static/tracker/latest3x/omega.min.js"></script>
  <script type="text/javascript"
    src="https://img-hxy021.didistatic.com/static/ep_static/dc-h5-js-sdk-0.0.64.js"></script>
    <script src="https://img-ys011.didistatic.com/static/iportal_static/app-cards-sdk-1.0.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        // 创建 PopupSDK 实例
        const popupDkSDK = new PopupSDK({
          // mountElement: document.getElementById('mount-point'), // 必填，挂载点元素
          mountSelector: '#iPortal-entry-dk', // 必填，挂载点元素
          // customClass: 'my-popup',
          appId: '13', // 必填
          appSecretKey: 'D6E2F41930564E5DB4ADC4E15565A8FB', // 必填
          secretKey: '6F37660DA3ED5F0D45D8A9F62D7A0D4A', // 必填
          ldap: getUserNameFromCookie(), // 必填
          onOpen: () => console.log('弹窗已打开'),
          onClose: () => console.log('弹窗已关闭'),
          onSelect: (item) => console.log('选中应用onSelect:', item),
        });
        window.popupDkSDK = popupDkSDK;
  
        // 监听事件
        // popupSDK.on('select', (item) => {
        //   console.log('选中应用select:', item);
        // });
  
        /**
         * 支持动态更新ldap、lang配置：
         * 1. 在前端框架中，可在watch监听器或useEffect钩子中调用updateConfig方法
         * 2. 对于异步获取的ldap、lang数据，可在用户点击图标前更新配置
         */
        // popupSDK.updateConfig({
        //     ldap: 'user-ldap',
        //     lang: 'en',
        // });
  
  
        /**
         * SDK实例提供编程式控制方法：
         * 1. open()：打开弹窗
         * 2. close()：关闭弹窗
         */
        // popupSDK.open(); // 主动触发弹窗显示，无需用户点击图标
        // popupSDK.close(); // 手动关闭弹窗，可用于超时或条件触发关闭
  
      });
    </script>
</body>

</html>
